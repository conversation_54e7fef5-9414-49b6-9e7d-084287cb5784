import 'package:api_client/api_client.dart';
import 'package:e_trader/fusion.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';
import 'package:socket_client/socket_client.dart';
import 'package:trader/di/di_initializer.dart';

DisplayableComponent tradingChartView() {
  return DisplayableComponent(
    title: "Trading Chart View",
    children: [
      DisplayableComponent(
        title: 'Success Advance',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/Symbol/get-chart': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/get_chart/get_chart_success.json',
                ),
              ],
            });
          diContainer<MockSocketInterceptor>().addMockResponse(
            url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/productHub",
            eventType: TradingSocketEvent.quotes.subscribe,
            responses: [
              {
                "symbol": "EURUSD",
                "ask": 0.90793,
                "bid": 0.90768,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Up",
                "midPrice": 76.492,
              },
              {
                "symbol": "EURUSD",
                "ask": 0.90753,
                "bid": 0.90762,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Down",
                "midPrice": 76.492,
              },
              {
                "symbol": "EURUSD",
                "ask": 0.90201,
                "bid": 0.90475,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Up",
                "midPrice": 76.492,
              },
            ],
          );
          return Scaffold(
            body: SafeArea(
              child: AdvanceTradingView(
                symbol: 'EURUSD',
                digit: 5,
                interactionsEnabled: false,
              ),
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Success Simple',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/Symbol/get-chart': [
                MockResponse(
                  code: 200,
                  bodyFilePath:
                      'resources/mocks/get_chart/get_chart_success.json',
                ),
              ],
            });
          diContainer<MockSocketInterceptor>().addMockResponse(
            url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/productHub",
            eventType: TradingSocketEvent.quotes.subscribe,
            responses: [
              {
                "symbol": "EURUSD",
                "ask": 0.90793,
                "bid": 0.90768,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Up",
                "midPrice": 76.492,
              },
              {
                "symbol": "EURUSD",
                "ask": 0.90753,
                "bid": 0.90762,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Down",
                "midPrice": 76.492,
              },
              {
                "symbol": "EURUSD",
                "ask": 0.90201,
                "bid": 0.90475,
                "date": "2024-11-22T05:15:03",
                "digits": 5,
                "dailyRateChange": 0,
                "direction": "Up",
                "midPrice": 76.492,
              },
            ],
          );
          return Scaffold(
            body: SafeArea(
              child: AdvanceTradingView(
                symbol: 'EURUSD',
                digit: 5,
                interactionsEnabled: false,
              ),
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Failure',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/Symbol/get-chart': [
                MockResponse(
                  code: 400,
                  bodyFilePath:
                      'resources/mocks/get-chart/get_chart_failure.json',
                ),
              ],
            });
          return Scaffold(
            body: SafeArea(
              child: AdvanceTradingView(
                symbol: 'EURUSD',
                digit: 5,
                interactionsEnabled: false,
              ),
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'Loading',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              'api/Symbol/get-chart': [
                MockResponse(
                  delayInMillis: 900000,
                  bodyFilePath:
                      'resources/mocks/get_chart/get_chart_success.json',
                ),
              ],
            });
          return Scaffold(
            body: SafeArea(
              child: AdvanceTradingView(
                symbol: 'EURUSD',
                digit: 5,
                interactionsEnabled: false,
              ),
            ),
          );
        },
      ),
    ],
  );
}
