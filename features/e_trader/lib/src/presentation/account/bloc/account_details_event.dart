part of 'account_details_bloc.dart';

@freezed
sealed class AccountDetailsEvent with _$AccountDetailsEvent {
  factory AccountDetailsEvent.subscribeToTradingAccountBalance() =
      _SubscribeToTradingAccountBalance;
  factory AccountDetailsEvent.updateTradingAccountBalance(EventType eventType) =
      _UpdateTradingAccountBalance;
  factory AccountDetailsEvent.processTradingAccountBalance(
    AccountBalanceHubResponse? accountBalanceHubResponse,
  ) = _ProcessTradingAccountBalance;
  factory AccountDetailsEvent.updateTradingAccountLeverage() =
      _UpdateTradingAccountLeverage;
  factory AccountDetailsEvent.processError(Object? error) =
      _ProcessAccountBalanceError;
}
