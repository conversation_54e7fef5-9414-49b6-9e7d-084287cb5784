// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'account_details_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AccountDetailsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsEvent()';
}


}

/// @nodoc
class $AccountDetailsEventCopyWith<$Res>  {
$AccountDetailsEventCopyWith(AccountDetailsEvent _, $Res Function(AccountDetailsEvent) __);
}


/// @nodoc


class _SubscribeToTradingAccountBalance implements AccountDetailsEvent {
   _SubscribeToTradingAccountBalance();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SubscribeToTradingAccountBalance);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsEvent.subscribeToTradingAccountBalance()';
}


}




/// @nodoc


class _UpdateTradingAccountBalance implements AccountDetailsEvent {
   _UpdateTradingAccountBalance(this.eventType);
  

 final  EventType eventType;

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateTradingAccountBalanceCopyWith<_UpdateTradingAccountBalance> get copyWith => __$UpdateTradingAccountBalanceCopyWithImpl<_UpdateTradingAccountBalance>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateTradingAccountBalance&&(identical(other.eventType, eventType) || other.eventType == eventType));
}


@override
int get hashCode => Object.hash(runtimeType,eventType);

@override
String toString() {
  return 'AccountDetailsEvent.updateTradingAccountBalance(eventType: $eventType)';
}


}

/// @nodoc
abstract mixin class _$UpdateTradingAccountBalanceCopyWith<$Res> implements $AccountDetailsEventCopyWith<$Res> {
  factory _$UpdateTradingAccountBalanceCopyWith(_UpdateTradingAccountBalance value, $Res Function(_UpdateTradingAccountBalance) _then) = __$UpdateTradingAccountBalanceCopyWithImpl;
@useResult
$Res call({
 EventType eventType
});




}
/// @nodoc
class __$UpdateTradingAccountBalanceCopyWithImpl<$Res>
    implements _$UpdateTradingAccountBalanceCopyWith<$Res> {
  __$UpdateTradingAccountBalanceCopyWithImpl(this._self, this._then);

  final _UpdateTradingAccountBalance _self;
  final $Res Function(_UpdateTradingAccountBalance) _then;

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? eventType = null,}) {
  return _then(_UpdateTradingAccountBalance(
null == eventType ? _self.eventType : eventType // ignore: cast_nullable_to_non_nullable
as EventType,
  ));
}


}

/// @nodoc


class _ProcessTradingAccountBalance implements AccountDetailsEvent {
   _ProcessTradingAccountBalance(this.accountBalanceHubResponse);
  

 final  AccountBalanceHubResponse? accountBalanceHubResponse;

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessTradingAccountBalanceCopyWith<_ProcessTradingAccountBalance> get copyWith => __$ProcessTradingAccountBalanceCopyWithImpl<_ProcessTradingAccountBalance>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessTradingAccountBalance&&(identical(other.accountBalanceHubResponse, accountBalanceHubResponse) || other.accountBalanceHubResponse == accountBalanceHubResponse));
}


@override
int get hashCode => Object.hash(runtimeType,accountBalanceHubResponse);

@override
String toString() {
  return 'AccountDetailsEvent.processTradingAccountBalance(accountBalanceHubResponse: $accountBalanceHubResponse)';
}


}

/// @nodoc
abstract mixin class _$ProcessTradingAccountBalanceCopyWith<$Res> implements $AccountDetailsEventCopyWith<$Res> {
  factory _$ProcessTradingAccountBalanceCopyWith(_ProcessTradingAccountBalance value, $Res Function(_ProcessTradingAccountBalance) _then) = __$ProcessTradingAccountBalanceCopyWithImpl;
@useResult
$Res call({
 AccountBalanceHubResponse? accountBalanceHubResponse
});


$AccountBalanceHubResponseCopyWith<$Res>? get accountBalanceHubResponse;

}
/// @nodoc
class __$ProcessTradingAccountBalanceCopyWithImpl<$Res>
    implements _$ProcessTradingAccountBalanceCopyWith<$Res> {
  __$ProcessTradingAccountBalanceCopyWithImpl(this._self, this._then);

  final _ProcessTradingAccountBalance _self;
  final $Res Function(_ProcessTradingAccountBalance) _then;

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? accountBalanceHubResponse = freezed,}) {
  return _then(_ProcessTradingAccountBalance(
freezed == accountBalanceHubResponse ? _self.accountBalanceHubResponse : accountBalanceHubResponse // ignore: cast_nullable_to_non_nullable
as AccountBalanceHubResponse?,
  ));
}

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountBalanceHubResponseCopyWith<$Res>? get accountBalanceHubResponse {
    if (_self.accountBalanceHubResponse == null) {
    return null;
  }

  return $AccountBalanceHubResponseCopyWith<$Res>(_self.accountBalanceHubResponse!, (value) {
    return _then(_self.copyWith(accountBalanceHubResponse: value));
  });
}
}

/// @nodoc


class _UpdateTradingAccountLeverage implements AccountDetailsEvent {
   _UpdateTradingAccountLeverage();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateTradingAccountLeverage);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsEvent.updateTradingAccountLeverage()';
}


}




/// @nodoc


class _ProcessAccountBalanceError implements AccountDetailsEvent {
   _ProcessAccountBalanceError(this.error);
  

 final  Object? error;

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProcessAccountBalanceErrorCopyWith<_ProcessAccountBalanceError> get copyWith => __$ProcessAccountBalanceErrorCopyWithImpl<_ProcessAccountBalanceError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProcessAccountBalanceError&&const DeepCollectionEquality().equals(other.error, error));
}


@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(error));

@override
String toString() {
  return 'AccountDetailsEvent.processError(error: $error)';
}


}

/// @nodoc
abstract mixin class _$ProcessAccountBalanceErrorCopyWith<$Res> implements $AccountDetailsEventCopyWith<$Res> {
  factory _$ProcessAccountBalanceErrorCopyWith(_ProcessAccountBalanceError value, $Res Function(_ProcessAccountBalanceError) _then) = __$ProcessAccountBalanceErrorCopyWithImpl;
@useResult
$Res call({
 Object? error
});




}
/// @nodoc
class __$ProcessAccountBalanceErrorCopyWithImpl<$Res>
    implements _$ProcessAccountBalanceErrorCopyWith<$Res> {
  __$ProcessAccountBalanceErrorCopyWithImpl(this._self, this._then);

  final _ProcessAccountBalanceError _self;
  final $Res Function(_ProcessAccountBalanceError) _then;

/// Create a copy of AccountDetailsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? error = freezed,}) {
  return _then(_ProcessAccountBalanceError(
freezed == error ? _self.error : error ,
  ));
}


}

/// @nodoc
mixin _$AccountDetailsState {

 AccountDetailsProcessState get accountProcessState; TradeAccountModel? get tradingAccountUpdates; TradingAccountModel? get accountdetails;
/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountDetailsStateCopyWith<AccountDetailsState> get copyWith => _$AccountDetailsStateCopyWithImpl<AccountDetailsState>(this as AccountDetailsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsState&&(identical(other.accountProcessState, accountProcessState) || other.accountProcessState == accountProcessState)&&(identical(other.tradingAccountUpdates, tradingAccountUpdates) || other.tradingAccountUpdates == tradingAccountUpdates)&&(identical(other.accountdetails, accountdetails) || other.accountdetails == accountdetails));
}


@override
int get hashCode => Object.hash(runtimeType,accountProcessState,tradingAccountUpdates,accountdetails);

@override
String toString() {
  return 'AccountDetailsState(accountProcessState: $accountProcessState, tradingAccountUpdates: $tradingAccountUpdates, accountdetails: $accountdetails)';
}


}

/// @nodoc
abstract mixin class $AccountDetailsStateCopyWith<$Res>  {
  factory $AccountDetailsStateCopyWith(AccountDetailsState value, $Res Function(AccountDetailsState) _then) = _$AccountDetailsStateCopyWithImpl;
@useResult
$Res call({
 AccountDetailsProcessState accountProcessState, TradeAccountModel? tradingAccountUpdates, TradingAccountModel? accountdetails
});


$AccountDetailsProcessStateCopyWith<$Res> get accountProcessState;$TradeAccountModelCopyWith<$Res>? get tradingAccountUpdates;$TradingAccountModelCopyWith<$Res>? get accountdetails;

}
/// @nodoc
class _$AccountDetailsStateCopyWithImpl<$Res>
    implements $AccountDetailsStateCopyWith<$Res> {
  _$AccountDetailsStateCopyWithImpl(this._self, this._then);

  final AccountDetailsState _self;
  final $Res Function(AccountDetailsState) _then;

/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountProcessState = null,Object? tradingAccountUpdates = freezed,Object? accountdetails = freezed,}) {
  return _then(_self.copyWith(
accountProcessState: null == accountProcessState ? _self.accountProcessState : accountProcessState // ignore: cast_nullable_to_non_nullable
as AccountDetailsProcessState,tradingAccountUpdates: freezed == tradingAccountUpdates ? _self.tradingAccountUpdates : tradingAccountUpdates // ignore: cast_nullable_to_non_nullable
as TradeAccountModel?,accountdetails: freezed == accountdetails ? _self.accountdetails : accountdetails // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,
  ));
}
/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountDetailsProcessStateCopyWith<$Res> get accountProcessState {
  
  return $AccountDetailsProcessStateCopyWith<$Res>(_self.accountProcessState, (value) {
    return _then(_self.copyWith(accountProcessState: value));
  });
}/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeAccountModelCopyWith<$Res>? get tradingAccountUpdates {
    if (_self.tradingAccountUpdates == null) {
    return null;
  }

  return $TradeAccountModelCopyWith<$Res>(_self.tradingAccountUpdates!, (value) {
    return _then(_self.copyWith(tradingAccountUpdates: value));
  });
}/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get accountdetails {
    if (_self.accountdetails == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.accountdetails!, (value) {
    return _then(_self.copyWith(accountdetails: value));
  });
}
}


/// @nodoc


class _sAccountDetailsState implements AccountDetailsState {
  const _sAccountDetailsState({this.accountProcessState = const AccountDetailsProcessState.loading(), this.tradingAccountUpdates, this.accountdetails});
  

@override@JsonKey() final  AccountDetailsProcessState accountProcessState;
@override final  TradeAccountModel? tradingAccountUpdates;
@override final  TradingAccountModel? accountdetails;

/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$sAccountDetailsStateCopyWith<_sAccountDetailsState> get copyWith => __$sAccountDetailsStateCopyWithImpl<_sAccountDetailsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _sAccountDetailsState&&(identical(other.accountProcessState, accountProcessState) || other.accountProcessState == accountProcessState)&&(identical(other.tradingAccountUpdates, tradingAccountUpdates) || other.tradingAccountUpdates == tradingAccountUpdates)&&(identical(other.accountdetails, accountdetails) || other.accountdetails == accountdetails));
}


@override
int get hashCode => Object.hash(runtimeType,accountProcessState,tradingAccountUpdates,accountdetails);

@override
String toString() {
  return 'AccountDetailsState(accountProcessState: $accountProcessState, tradingAccountUpdates: $tradingAccountUpdates, accountdetails: $accountdetails)';
}


}

/// @nodoc
abstract mixin class _$sAccountDetailsStateCopyWith<$Res> implements $AccountDetailsStateCopyWith<$Res> {
  factory _$sAccountDetailsStateCopyWith(_sAccountDetailsState value, $Res Function(_sAccountDetailsState) _then) = __$sAccountDetailsStateCopyWithImpl;
@override @useResult
$Res call({
 AccountDetailsProcessState accountProcessState, TradeAccountModel? tradingAccountUpdates, TradingAccountModel? accountdetails
});


@override $AccountDetailsProcessStateCopyWith<$Res> get accountProcessState;@override $TradeAccountModelCopyWith<$Res>? get tradingAccountUpdates;@override $TradingAccountModelCopyWith<$Res>? get accountdetails;

}
/// @nodoc
class __$sAccountDetailsStateCopyWithImpl<$Res>
    implements _$sAccountDetailsStateCopyWith<$Res> {
  __$sAccountDetailsStateCopyWithImpl(this._self, this._then);

  final _sAccountDetailsState _self;
  final $Res Function(_sAccountDetailsState) _then;

/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountProcessState = null,Object? tradingAccountUpdates = freezed,Object? accountdetails = freezed,}) {
  return _then(_sAccountDetailsState(
accountProcessState: null == accountProcessState ? _self.accountProcessState : accountProcessState // ignore: cast_nullable_to_non_nullable
as AccountDetailsProcessState,tradingAccountUpdates: freezed == tradingAccountUpdates ? _self.tradingAccountUpdates : tradingAccountUpdates // ignore: cast_nullable_to_non_nullable
as TradeAccountModel?,accountdetails: freezed == accountdetails ? _self.accountdetails : accountdetails // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,
  ));
}

/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountDetailsProcessStateCopyWith<$Res> get accountProcessState {
  
  return $AccountDetailsProcessStateCopyWith<$Res>(_self.accountProcessState, (value) {
    return _then(_self.copyWith(accountProcessState: value));
  });
}/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradeAccountModelCopyWith<$Res>? get tradingAccountUpdates {
    if (_self.tradingAccountUpdates == null) {
    return null;
  }

  return $TradeAccountModelCopyWith<$Res>(_self.tradingAccountUpdates!, (value) {
    return _then(_self.copyWith(tradingAccountUpdates: value));
  });
}/// Create a copy of AccountDetailsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get accountdetails {
    if (_self.accountdetails == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.accountdetails!, (value) {
    return _then(_self.copyWith(accountdetails: value));
  });
}
}

/// @nodoc
mixin _$AccountDetailsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsProcessState()';
}


}

/// @nodoc
class $AccountDetailsProcessStateCopyWith<$Res>  {
$AccountDetailsProcessStateCopyWith(AccountDetailsProcessState _, $Res Function(AccountDetailsProcessState) __);
}


/// @nodoc


class AccountDetailsLoadingProcessState implements AccountDetailsProcessState {
  const AccountDetailsLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsProcessState.loading()';
}


}




/// @nodoc


class AccountDetailsConnectedProcessState implements AccountDetailsProcessState {
  const AccountDetailsConnectedProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsConnectedProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsProcessState.connected()';
}


}




/// @nodoc


class AccountDetailsSuccessProcessState implements AccountDetailsProcessState {
  const AccountDetailsSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsProcessState.success()';
}


}




/// @nodoc


class AccountDetailsErrorProcessState implements AccountDetailsProcessState {
  const AccountDetailsErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsProcessState.error()';
}


}




/// @nodoc


class AccountDetailsEmptyProcessState implements AccountDetailsProcessState {
  const AccountDetailsEmptyProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountDetailsEmptyProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountDetailsProcessState.empty()';
}


}




// dart format on
